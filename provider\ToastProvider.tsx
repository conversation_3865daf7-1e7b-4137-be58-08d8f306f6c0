"use client";

import "react-toastify/dist/ReactToastify.css";
import "@/app/globals.css";
import { ToastContainer } from "react-toastify";
import { useAppStore } from "@/store";
import Header from "@/components/Common/Header";

interface ToastProviderProps {
  children: React.ReactNode;
}

export default function ToastProvider({ children }: ToastProviderProps) {
  const { theme } = useAppStore();

  return (
    <div data-theme={theme}>
      <Header />

      {children}
      <ToastContainer />
    </div>
  );
}
