"use client";
import { useState, useEffect, useCallback } from 'react';
import { MovieListParams, TypeList, SortField, SortType, SortLang } from '@/type/MovieListParams';
import { Item } from '@/type/ListMovieRespone';
import { getListMovieByType } from '@/service/KKPhimService';

interface UseMovieFilterOptions {
  initialFilters?: Partial<MovieListParams>;
  autoFetch?: boolean;
  debounceMs?: number;
}

interface UseMovieFilterReturn {
  // Filter state
  filters: MovieListParams;
  setFilters: (filters: MovieListParams) => void;
  updateFilter: <K extends keyof MovieListParams>(key: K, value: MovieListParams[K]) => void;
  resetFilters: () => void;
  
  // Data state
  movies: Item[];
  loading: boolean;
  error: string | null;
  
  // Pagination
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  
  // Actions
  fetchMovies: () => Promise<void>;
  nextPage: () => void;
  prevPage: () => void;
  goToPage: (page: number) => void;
  
  // Utilities
  getActiveFiltersCount: () => number;
  isFilterActive: (key: keyof MovieListParams) => boolean;
  clearFilter: (key: keyof MovieListParams) => void;
}

const defaultFilters: MovieListParams = {
  type_list: 'phim-le',
  page: 1,
  sort_field: 'modified.time',
  sort_type: 'desc',
  limit: 24
};

export const useMovieFilter = (options: UseMovieFilterOptions = {}): UseMovieFilterReturn => {
  const {
    initialFilters = {},
    autoFetch = true,
    debounceMs = 300
  } = options;

  // State
  const [filters, setFiltersState] = useState<MovieListParams>({
    ...defaultFilters,
    ...initialFilters
  });
  
  const [movies, setMovies] = useState<Item[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    hasNextPage: false,
    hasPrevPage: false
  });

  // Debounced fetch
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const fetchMovies = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await getListMovieByType(filters);
      
      if (response.data?.items) {
        setMovies(response.data.items);
        
        // Calculate pagination (API might not provide this)
        const totalItems = response.data.items.length;
        const totalPages = Math.max(1, Math.ceil(totalItems / (filters.limit || 24)));
        const currentPage = filters.page || 1;
        
        setPagination({
          currentPage,
          totalPages,
          totalItems,
          hasNextPage: currentPage < totalPages,
          hasPrevPage: currentPage > 1
        });
      } else {
        setMovies([]);
        setPagination({
          currentPage: 1,
          totalPages: 1,
          totalItems: 0,
          hasNextPage: false,
          hasPrevPage: false
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải dữ liệu';
      setError(errorMessage);
      setMovies([]);
      setPagination({
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        hasNextPage: false,
        hasPrevPage: false
      });
      console.error('Error fetching movies:', err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Auto fetch when filters change
  useEffect(() => {
    if (!autoFetch) return;

    // Clear existing timer
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Set new timer
    const timer = setTimeout(() => {
      fetchMovies();
    }, debounceMs);

    setDebounceTimer(timer);

    // Cleanup
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [filters, autoFetch, debounceMs, fetchMovies]);

  // Filter management
  const setFilters = useCallback((newFilters: MovieListParams) => {
    setFiltersState(newFilters);
  }, []);

  const updateFilter = useCallback(<K extends keyof MovieListParams>(
    key: K, 
    value: MovieListParams[K]
  ) => {
    setFiltersState(prev => ({
      ...prev,
      [key]: value,
      // Reset page when changing filters (except page itself)
      ...(key !== 'page' && { page: 1 })
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFiltersState({ ...defaultFilters, ...initialFilters });
  }, [initialFilters]);

  const clearFilter = useCallback((key: keyof MovieListParams) => {
    setFiltersState(prev => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return {
        ...newFilters,
        page: 1 // Reset page when clearing filter
      };
    });
  }, []);

  // Pagination
  const nextPage = useCallback(() => {
    if (pagination.hasNextPage) {
      updateFilter('page', pagination.currentPage + 1);
    }
  }, [pagination.hasNextPage, pagination.currentPage, updateFilter]);

  const prevPage = useCallback(() => {
    if (pagination.hasPrevPage) {
      updateFilter('page', pagination.currentPage - 1);
    }
  }, [pagination.hasPrevPage, pagination.currentPage, updateFilter]);

  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= pagination.totalPages) {
      updateFilter('page', page);
    }
  }, [pagination.totalPages, updateFilter]);

  // Utilities
  const getActiveFiltersCount = useCallback(() => {
    let count = 0;
    if (filters.category) count++;
    if (filters.country) count++;
    if (filters.year) count++;
    if (filters.sort_lang) count++;
    if (filters.sort_field !== defaultFilters.sort_field || filters.sort_type !== defaultFilters.sort_type) count++;
    return count;
  }, [filters]);

  const isFilterActive = useCallback((key: keyof MovieListParams) => {
    return filters[key] !== undefined && filters[key] !== defaultFilters[key];
  }, [filters]);

  return {
    // Filter state
    filters,
    setFilters,
    updateFilter,
    resetFilters,
    
    // Data state
    movies,
    loading,
    error,
    
    // Pagination
    pagination,
    
    // Actions
    fetchMovies,
    nextPage,
    prevPage,
    goToPage,
    
    // Utilities
    getActiveFiltersCount,
    isFilterActive,
    clearFilter
  };
};

export default useMovieFilter;
