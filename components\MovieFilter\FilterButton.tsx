"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { Filter, X, RotateCcw } from "lucide-react";
import quocGia from "@/constants/quocgia.json";
import theLoai from "@/constants/theloai.json";
import { FiFilter } from "react-icons/fi";

const TypeList = [
  { id: "tat-ca", slug: "tat-ca", name: "Tất Cả" },
  { id: "phim-le", slug: "phim-le", name: "Phim Lẻ" },
  { id: "phim-bo", slug: "phim-bo", name: "<PERSON><PERSON>" },
  { id: "tv-shows", slug: "tv-shows", name: "TV Shows" },
  { id: "hoat-hinh", slug: "hoat-hinh", name: "<PERSON><PERSON><PERSON> Hình" },
  { id: "phim-vietsub", slug: "phim-vietsub", name: "<PERSON><PERSON> V<PERSON>sub" },
  {
    id: "phim-thuyet-minh",
    slug: "phim-thuyet-minh",
    name: "<PERSON><PERSON>ết <PERSON>",
  },
  { id: "phim-long-tieng", slug: "phim-long-tieng", name: "Phim Lồng Tiếng" },
];

const SortFieldList = [
  { id: "modified.time", slug: "modified.time", name: "Mới cập nhật" },
  { id: "_id", slug: "_id", name: "Mới đăng" },
  { id: "year", slug: "year", name: "Năm phát hành" },
];

const YearList = [
  { id: "2025", slug: "2025", name: "2025" },
  { id: "2024", slug: "2024", name: "2024" },
  { id: "2023", slug: "2023", name: "2023" },
  { id: "2022", slug: "2022", name: "2022" },
  { id: "2021", slug: "2021", name: "2021" },
  { id: "2020", slug: "2020", name: "2020" },
  { id: "2019", slug: "2019", name: "2019" },
  { id: "2018", slug: "2018", name: "2018" },
  { id: "2017", slug: "2017", name: "2017" },
  { id: "2016", slug: "2016", name: "2016" },
  { id: "2015", slug: "2015", name: "2015" },
  { id: "2014", slug: "2014", name: "2014" },
  { id: "2013", slug: "2013", name: "2013" },
  { id: "2012", slug: "2012", name: "2012" },
  { id: "2011", slug: "2011", name: "2011" },
  { id: "2010", slug: "2010", name: "2010" },
];

const FilterButton = () => {
  const [showFilter, setShowFilter] = React.useState(false);
  return (
    <div className="relative">
      <button
        onClick={() => setShowFilter(!showFilter)}
        className="btn btn-ghost btn-sm"
      >
        <FiFilter /> Bộ lọc
        {showFilter && (
          <div className="absolute top-0 right-0 z-10 w-full h-[1px] bg-amber-400">
            {" "}
          </div>
        )}
      </button>

      {showFilter && (
        <div className="flex flex-col">
          <div className="flex flex-row gap-x-4">
            <div className="">
              <span>Quốc gia:</span>
            </div>
            <div className="flex flex-row flex-wrap gap-x-4 flex-1">
              {quocGia.map((item) => (
                <button className="btn btn-ghost" key={item._id}>
                  {item.name}
                </button>
              ))}
            </div>
          </div>
          <div className="flex flex-row gap-x-4">
            <div className="">
              <span>Loại phim: </span>
            </div>
            <div className="flex flex-row flex-wrap gap-x-4 flex-1">
              {TypeList.map((item) => (
                <button className="btn btn-ghost" key={item.id}>
                  {item.name}
                </button>
              ))}
            </div>
          </div>
          <div className="flex flex-row gap-x-4">
            <div className="">
              <span>Thể loại:</span>
            </div>
            <div className="flex flex-row flex-wrap gap-x-4 flex-1">
              {theLoai.map((item) => (
                <button className="btn btn-ghost" key={item._id}>
                  {item.name}
                </button>
              ))}
            </div>
          </div>
          <div className="flex flex-row gap-x-4">
            <div className="">
              <span>Sắp xếp:</span>
            </div>
            <div className="flex flex-row flex-wrap gap-x-4 flex-1">
              {SortFieldList.map((item) => (
                <button className="btn btn-ghost" key={item.id}>
                  {item.name}
                </button>
              ))}
            </div>
          </div>
          <div className="flex flex-row gap-x-4">
            <div className="">
              <span>Năm phát hành:</span>
            </div>
            <div className="flex flex-row flex-wrap gap-x-4 flex-1">
              {YearList.map((item) => (
                <button className="btn btn-ghost" key={item.id}>
                  {item.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterButton;
