{"name": "hzphim", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroui/pagination": "^2.2.17", "classnames": "^2.5.1", "framer-motion": "^12.12.1", "lucide": "^0.511.0", "lucide-react": "^0.511.0", "next": "15.3.2", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "plyr-react": "^5.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-player": "^2.16.0", "react-toastify": "^11.0.5", "react-window": "^1.8.11", "swiper": "^11.2.7", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "daisyui": "^5.0.35", "tailwindcss": "^4", "typescript": "^5"}}