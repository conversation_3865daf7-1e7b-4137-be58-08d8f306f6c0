import React from "react";
import Link from "next/link";
import theLoai from "@/constants/theloai.json";
import { Film, Star, ArrowRight, Grid3X3 } from "lucide-react";

const TheLoaiPage = () => {
  // <PERSON><PERSON><PERSON> sắc đẹp cho các thẻ thể loại
  const getCardColors = (index: number) => {
    const colors = [
      {
        bg: "bg-gradient-to-br from-red-50 to-pink-50",
        border: "border-red-200 hover:border-red-400",
        icon: "bg-gradient-to-br from-red-100 to-pink-100",
        iconHover: "group-hover:from-red-200 group-hover:to-pink-200",
        text: "group-hover:text-red-600",
      },
      {
        bg: "bg-gradient-to-br from-blue-50 to-cyan-50",
        border: "border-blue-200 hover:border-blue-400",
        icon: "bg-gradient-to-br from-blue-100 to-cyan-100",
        iconHover: "group-hover:from-blue-200 group-hover:to-cyan-200",
        text: "group-hover:text-blue-600",
      },
      {
        bg: "bg-gradient-to-br from-green-50 to-emerald-50",
        border: "border-green-200 hover:border-green-400",
        icon: "bg-gradient-to-br from-green-100 to-emerald-100",
        iconHover: "group-hover:from-green-200 group-hover:to-emerald-200",
        text: "group-hover:text-green-600",
      },
      {
        bg: "bg-gradient-to-br from-purple-50 to-violet-50",
        border: "border-purple-200 hover:border-purple-400",
        icon: "bg-gradient-to-br from-purple-100 to-violet-100",
        iconHover: "group-hover:from-purple-200 group-hover:to-violet-200",
        text: "group-hover:text-purple-600",
      },
      {
        bg: "bg-gradient-to-br from-orange-50 to-amber-50",
        border: "border-orange-200 hover:border-orange-400",
        icon: "bg-gradient-to-br from-orange-100 to-amber-100",
        iconHover: "group-hover:from-orange-200 group-hover:to-amber-200",
        text: "group-hover:text-orange-600",
      },
      {
        bg: "bg-gradient-to-br from-teal-50 to-cyan-50",
        border: "border-teal-200 hover:border-teal-400",
        icon: "bg-gradient-to-br from-teal-100 to-cyan-100",
        iconHover: "group-hover:from-teal-200 group-hover:to-cyan-200",
        text: "group-hover:text-teal-600",
      },
      {
        bg: "bg-gradient-to-br from-indigo-50 to-blue-50",
        border: "border-indigo-200 hover:border-indigo-400",
        icon: "bg-gradient-to-br from-indigo-100 to-blue-100",
        iconHover: "group-hover:from-indigo-200 group-hover:to-blue-200",
        text: "group-hover:text-indigo-600",
      },
      {
        bg: "bg-gradient-to-br from-rose-50 to-pink-50",
        border: "border-rose-200 hover:border-rose-400",
        icon: "bg-gradient-to-br from-rose-100 to-pink-100",
        iconHover: "group-hover:from-rose-200 group-hover:to-pink-200",
        text: "group-hover:text-rose-600",
      },
    ];
    return colors[index % colors.length];
  };

  return (
    <div className="min-h-screen pt-20 bg-base-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-3 mb-8">
          <div className="p-3 bg-primary/10 rounded-xl">
            <Grid3X3 className="w-8 h-8 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-base-content">
              Thể loại phim
            </h1>
            <p className="text-base-content/70">
              Khám phá phim theo thể loại yêu thích của bạn
            </p>
          </div>
        </div>

        {/* Stats */}
        <div className="stats shadow mb-8">
          <div className="stat">
            <div className="stat-figure text-primary">
              <Film className="w-8 h-8" />
            </div>
            <div className="stat-title">Tổng số thể loại</div>
            <div className="stat-value text-primary">{theLoai.length}</div>
            <div className="stat-desc">Đa dạng và phong phú</div>
          </div>
        </div>

        {/* Genre Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
          {theLoai.map((genre, index) => {
            const colors = getCardColors(index);
            return (
              <Link
                key={genre._id}
                href={`/the-loai/${genre.slug}`}
                className="group"
              >
                <div
                  className={`card shadow-md border-2 hover:shadow-xl transition-all duration-300 group-hover:scale-105 ${colors.bg} ${colors.border}`}
                >
                  <div className="card-body p-4">
                    {/* Genre Icon */}
                    <div className="flex items-center justify-center mb-3">
                      <div
                        className={`p-3 rounded-full transition-colors ${colors.icon} ${colors.iconHover}`}
                      >
                        <Star className="w-6 h-6 text-gray-600" />
                      </div>
                    </div>

                    {/* Genre Name */}
                    <h3
                      className={`text-center font-semibold text-gray-700 transition-colors ${colors.text}`}
                    >
                      {genre.name}
                    </h3>

                    {/* Arrow Icon */}
                    <div className="flex justify-center mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <ArrowRight className="w-4 h-4 text-gray-600" />
                    </div>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default TheLoaiPage;
