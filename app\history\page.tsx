"use client";
import React, { useState } from "react";
import WatchedMovieCard from "@/components/MovieCard/WatchedMovieCard";
import { useContinueWatchingList } from "@/hooks/useContinueWatching";
import { History, Search, Trash2, <PERSON><PERSON><PERSON><PERSON>gle, X } from "lucide-react";

const HistoryPage = () => {
  const { recentItems, removeItem } = useContinueWatchingList();
  const [showClearModal, setShowClearModal] = useState(false);

  const handleRemoveFromHistory = (movieId: string) => {
    removeItem(movieId);
  };

  const handleClearAll = () => {
    setShowClearModal(true);
  };

  const confirmClearAll = () => {
    recentItems.forEach((item) => {
      removeItem(item.movie._id);
    });
    setShowClearModal(false);
  };

  if (recentItems.length === 0) {
    return (
      <div className="min-h-screen pt-20 bg-base-100">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center max-w-md mx-auto">
            <div className="w-24 h-24 mx-auto mb-6 bg-base-200 rounded-full flex items-center justify-center">
              <History className="w-12 h-12 text-base-content/30" />
            </div>
            <h1 className="text-3xl font-bold text-base-content mb-4">
              Chưa có lịch sử xem
            </h1>
            <p className="text-base-content/70 mb-8">
              Hãy bắt đầu xem phim để tạo lịch sử xem của bạn!
            </p>
            <a href="/" className="btn btn-primary gap-2">
              <Search className="w-4 h-4" />
              Khám phá phim
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 bg-base-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-xl">
              <History className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-base-content">
                Lịch sử xem
              </h1>
              <p className="text-base-content/70">
                {recentItems.length} bộ phim
              </p>
            </div>
          </div>

          {recentItems.length > 0 && (
            <button
              onClick={handleClearAll}
              className="btn btn-outline btn-error btn-sm gap-2"
            >
              <Trash2 className="w-4 h-4" />
              Xóa tất cả
            </button>
          )}
        </div>

        {/* Movies Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
          {recentItems.map((item, index) => (
            <WatchedMovieCard
              key={`${item.movie._id}-${item.ep}-${item.ver}`}
              item={item}
              index={index}
              showProgress
              variant="grid"
              showRemoveButton
              showWatchInfo
              onRemove={() => handleRemoveFromHistory(item.movie._id)}
            />
          ))}
        </div>

        {/* Clear All Modal */}
        {showClearModal && (
          <div className="modal modal-open">
            <div className="modal-box">
              {/* Modal Header */}
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-error/10 rounded-lg">
                  <AlertTriangle className="w-6 h-6 text-error" />
                </div>
                <h3 className="font-bold text-lg">Xác nhận xóa lịch sử</h3>
              </div>

              {/* Modal Content */}
              <p className="text-base-content/70 mb-6">
                Bạn có chắc chắn muốn xóa tất cả{" "}
                <span className="font-semibold text-primary">
                  {recentItems.length} bộ phim
                </span>{" "}
                khỏi lịch sử xem không? Hành động này không thể hoàn tác.
              </p>

              <div className="alert alert-warning mb-4">
                <AlertTriangle className="w-4 h-4" />
                <span className="text-sm">
                  Bạn sẽ mất thông tin tiến độ xem của tất cả các phim.
                </span>
              </div>

              {/* Modal Actions */}
              <div className="modal-action">
                <button
                  onClick={() => setShowClearModal(false)}
                  className="btn btn-outline gap-2"
                >
                  <X className="w-4 h-4" />
                  Hủy
                </button>
                <button
                  onClick={confirmClearAll}
                  className="btn btn-error gap-2"
                >
                  <Trash2 className="w-4 h-4" />
                  Xóa lịch sử
                </button>
              </div>
            </div>
            <div
              className="modal-backdrop"
              onClick={() => setShowClearModal(false)}
            ></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HistoryPage;
