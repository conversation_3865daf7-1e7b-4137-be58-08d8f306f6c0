import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/Common/Header";
import NextTopLoader from "nextjs-toploader";
import ToastProvider from "@/provider/ToastProvider";
const roboto_Serif = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Hz phim",
  description: "Đỉnh cao phim ảnh",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" data-theme="dark" suppressHydrationWarning>
      <body className={`${roboto_Serif.variable} antialiased`}>
        <NextTopLoader showSpinner={false} color="var(--color-primary)" />
        <Header />
        <ToastProvider>{children}</ToastProvider>
      </body>
    </html>
  );
}
