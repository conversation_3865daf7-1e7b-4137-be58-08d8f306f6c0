"use client";
import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Paintbrush,
  Eye,
} from "lucide-react";
import { useAppStore } from "@/store";

// DaisyUI themes organized by light/dark
const LIGHT_THEMES = [
  "light",
  "cupcake",
  "bumblebee",
  "emerald",
  "corporate",
  "retro",
  "cyberpunk",
  "valentine",
  "garden",
  "lofi",
  "pastel",
  "fantasy",
  "wireframe",
  "cmyk",
  "autumn",
  "acid",
  "lemonade",
  "winter",
];

const DARK_THEMES = [
  "dark",
  "synthwave",
  "halloween",
  "forest",
  "aqua",
  "luxury",
  "dracula",
  "night",
  "coffee",
  "dim",
  "nord",
  "sunset",
  "business",
];

const SettingsPage = () => {
  const [themeCategory, setThemeCategory] = useState<"light" | "dark">("light");
  const { theme, setTheme } = useAppStore();

  // Load theme from localStorage on mount

  const handleThemeChange = (theme: string) => {
    setTheme(theme);
  };

  const getThemeDisplayName = (theme: string) => {
    return theme.charAt(0).toUpperCase() + theme.slice(1);
  };

  const getThemesByCategory = () => {
    return themeCategory === "light" ? LIGHT_THEMES : DARK_THEMES;
  };

  return (
    <div className="min-h-screen pt-20 bg-base-100">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="flex items-center gap-3 mb-8">
          <div className="p-3 bg-primary/10 rounded-xl">
            <Settings className="w-8 h-8 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-base-content">Cài đặt</h1>
            <p className="text-base-content/70">
              Tùy chỉnh giao diện và trải nghiệm của bạn
            </p>
          </div>
        </div>

        {/* Theme Settings Card */}
        <div className="card bg-base-100 shadow-xl border border-base-300 mb-6">
          <div className="card-body">
            {/* Section Header */}
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-secondary/10 rounded-lg">
                <Palette className="w-6 h-6 text-secondary" />
              </div>
              <div>
                <h2 className="card-title text-xl">Giao diện</h2>
                <p className="text-base-content/60 text-sm">
                  Chọn theme phù hợp với sở thích của bạn
                </p>
              </div>
            </div>

            {/* Current Theme Display */}
            <div className="alert alert-info mb-6">
              <Paintbrush className="w-5 h-5" />
              <div>
                <div className="font-semibold">Theme hiện tại</div>
                <div className="text-sm opacity-80">
                  {getThemeDisplayName(theme)}
                </div>
              </div>
            </div>

            {/* Theme Category Toggle */}
            <div className="flex items-center justify-center mb-6 ">
              <div className="btn-group">
                <button
                  onClick={() => setThemeCategory("light")}
                  className={`btn gap-2 ${
                    themeCategory === "light" ? "btn-primary" : "btn-outline"
                  }`}
                >
                  <Sun className="w-4 h-4" />
                  Sáng ({LIGHT_THEMES.length})
                </button>
                <button
                  onClick={() => setThemeCategory("dark")}
                  className={`btn gap-2 ${
                    themeCategory === "dark" ? "btn-primary" : "btn-outline"
                  }`}
                >
                  <Moon className="w-4 h-4" />
                  Tối ({DARK_THEMES.length})
                </button>
              </div>
            </div>

            {/* Theme Grid */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {getThemesByCategory().map((t) => (
                <div
                  key={t}
                  className={`relative cursor-pointer transition-all duration-200 hover:scale-105 ${
                    theme === t
                      ? "ring-2 ring-primary ring-offset-2 ring-offset-base-100"
                      : ""
                  }`}
                  onClick={() => handleThemeChange(t)}
                >
                  {/* Theme Preview Card */}
                  <div
                    className="card bg-base-100 shadow-md border border-base-300 overflow-hidden"
                    data-theme={t}
                  >
                    {/* Color Preview */}
                    <div className="h-16 flex">
                      <div className="flex-1 bg-primary"></div>
                      <div className="flex-1 bg-secondary"></div>
                      <div className="flex-1 bg-accent"></div>
                      <div className="flex-1 bg-neutral"></div>
                    </div>

                    {/* Theme Info */}
                    <div className="p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-sm text-base-content">
                            {getThemeDisplayName(t)}
                          </h3>
                          <div className="flex items-center gap-1 mt-1">
                            <div className="w-2 h-2 bg-primary rounded-full"></div>
                            <div className="w-2 h-2 bg-secondary rounded-full"></div>
                            <div className="w-2 h-2 bg-accent rounded-full"></div>
                          </div>
                        </div>

                        {theme === t && (
                          <div className="badge badge-primary badge-sm">
                            <Check className="w-3 h-3" />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Theme Info */}
            <div className="mt-6 p-4 bg-base-200 rounded-lg">
              <div className="flex items-start gap-3">
                <Eye className="w-5 h-5 text-info mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-base-content mb-1">
                    Mẹo sử dụng theme
                  </p>
                  <ul className="text-base-content/70 space-y-1">
                    <li>• Click vào theme để xem trước và áp dụng</li>
                    <li>• Theme sẽ được lưu tự động trong trình duyệt</li>
                    <li>
                      • Thử các theme khác nhau để tìm ra sở thích của bạn
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Settings Placeholder */}
        <div className="card bg-base-100 shadow-xl border border-base-300">
          <div className="card-body">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-accent/10 rounded-lg">
                <Monitor className="w-6 h-6 text-accent" />
              </div>
              <div>
                <h2 className="card-title text-xl">Cài đặt khác</h2>
                <p className="text-base-content/60 text-sm">
                  Các tùy chọn bổ sung sẽ được thêm trong tương lai
                </p>
              </div>
            </div>

            <div className="text-center py-8 text-base-content/50">
              <Monitor className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>Các cài đặt khác đang được phát triển...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
