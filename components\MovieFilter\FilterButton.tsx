"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { Filter, X, RotateCcw, ChevronDown, Check } from "lucide-react";
import quocGia from "@/constants/quocgia.json";
import theLoai from "@/constants/theloai.json";
import { FiFilter } from "react-icons/fi";

const TypeList = [
  { id: "tat-ca", slug: "tat-ca", name: "Tất <PERSON>ả" },
  { id: "phim-le", slug: "phim-le", name: "Phim Lẻ" },
  { id: "phim-bo", slug: "phim-bo", name: "<PERSON><PERSON>ộ" },
  { id: "tv-shows", slug: "tv-shows", name: "TV Shows" },
  { id: "hoat-hinh", slug: "hoat-hinh", name: "<PERSON><PERSON><PERSON> Hình" },
  { id: "phim-vietsub", slug: "phim-vietsub", name: "<PERSON><PERSON> Vietsub" },
  {
    id: "phim-thuyet-minh",
    slug: "phim-thuyet-minh",
    name: "Phim Thuyết <PERSON>",
  },
  { id: "phim-long-tieng", slug: "phim-long-tieng", name: "Phim Lồng Tiếng" },
];

const SortFieldList = [
  { id: "modified.time", slug: "modified.time", name: "Mới cập nhật" },
  { id: "_id", slug: "_id", name: "Mới đăng" },
  { id: "year", slug: "year", name: "Năm phát hành" },
];

const YearList = [
  { id: "2025", slug: "2025", name: "2025" },
  { id: "2024", slug: "2024", name: "2024" },
  { id: "2023", slug: "2023", name: "2023" },
  { id: "2022", slug: "2022", name: "2022" },
  { id: "2021", slug: "2021", name: "2021" },
  { id: "2020", slug: "2020", name: "2020" },
  { id: "2019", slug: "2019", name: "2019" },
  { id: "2018", slug: "2018", name: "2018" },
  { id: "2017", slug: "2017", name: "2017" },
  { id: "2016", slug: "2016", name: "2016" },
  { id: "2015", slug: "2015", name: "2015" },
  { id: "2014", slug: "2014", name: "2014" },
  { id: "2013", slug: "2013", name: "2013" },
  { id: "2012", slug: "2012", name: "2012" },
  { id: "2011", slug: "2011", name: "2011" },
  { id: "2010", slug: "2010", name: "2010" },
];

interface FilterState {
  country: string;
  type: string;
  genre: string;
  sort: string;
  year: string;
}

const FilterButton = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [showFilter, setShowFilter] = useState(false);
  const [activeFilters, setActiveFilters] = useState<FilterState>({
    country: searchParams.get("country") || "",
    type: searchParams.get("type") || "",
    genre: searchParams.get("genre") || "",
    sort: searchParams.get("sort") || "",
    year: searchParams.get("year") || "",
  });

  // Update URL when filters change
  const updateURL = (newFilters: FilterState) => {
    const params = new URLSearchParams();
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) params.set(key, value);
    });

    const queryString = params.toString();
    const newURL = queryString ? `?${queryString}` : window.location.pathname;
    router.push(newURL);
  };

  const handleFilterChange = (filterType: keyof FilterState, value: string) => {
    const newFilters = {
      ...activeFilters,
      [filterType]: activeFilters[filterType] === value ? "" : value,
    };
    setActiveFilters(newFilters);
    updateURL(newFilters);
  };

  const clearAllFilters = () => {
    const emptyFilters = {
      country: "",
      type: "",
      genre: "",
      sort: "",
      year: "",
    };
    setActiveFilters(emptyFilters);
    updateURL(emptyFilters);
  };

  const getActiveFilterCount = () => {
    return Object.values(activeFilters).filter(Boolean).length;
  };

  const FilterSection = ({
    title,
    items,
    filterKey,
    idKey = "_id",
  }: {
    title: string;
    items: any[];
    filterKey: keyof FilterState;
    idKey?: string;
  }) => (
    <div className="space-y-3">
      <h4 className="font-semibold text-base-content/80 text-sm uppercase tracking-wider">
        {title}
      </h4>
      <div className="flex flex-wrap gap-2">
        {items.map((item) => {
          const itemId = item[idKey] || item.id;
          const isActive = activeFilters[filterKey] === itemId;
          return (
            <motion.button
              key={itemId}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => handleFilterChange(filterKey, itemId)}
              className={`btn btn-sm transition-all duration-200 ${
                isActive
                  ? "btn-primary shadow-lg"
                  : "btn-outline btn-ghost hover:btn-primary hover:shadow-md"
              }`}
            >
              {isActive && <Check className="w-3 h-3 mr-1" />}
              {item.name}
            </motion.button>
          );
        })}
      </div>
    </div>
  );

  return (
    <div className="w-full">
      {/* Filter Toggle Button */}
      <div className="flex items-center justify-between mb-6">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setShowFilter(!showFilter)}
          className={`btn btn-outline gap-2 transition-all duration-300 ${
            showFilter ? "btn-primary" : ""
          }`}
        >
          <FiFilter className="w-4 h-4" />
          <span>Bộ lọc</span>
          {getActiveFilterCount() > 0 && (
            <div className="badge badge-primary badge-sm">
              {getActiveFilterCount()}
            </div>
          )}
          <ChevronDown
            className={`w-4 h-4 transition-transform duration-200 ${
              showFilter ? "rotate-180" : ""
            }`}
          />
        </motion.button>

        {getActiveFilterCount() > 0 && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={clearAllFilters}
            className="btn btn-ghost gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            Xóa tất cả ({getActiveFilterCount()})
          </motion.button>
        )}
      </div>

      {/* Filter Content */}
      <AnimatePresence>
        {showFilter && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="card bg-base-100 shadow-xl border border-base-300">
              <div className="card-body p-6">
                {/* Header */}
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-3 bg-primary/10 rounded-xl">
                    <Filter className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="card-title text-xl">Bộ lọc phim</h3>
                    <p className="text-base-content/60">
                      Tìm phim theo sở thích của bạn
                    </p>
                  </div>
                </div>

                {/* Filter Sections */}
                <div className="space-y-8">
                  <FilterSection
                    title="Loại phim"
                    items={TypeList}
                    filterKey="type"
                    idKey="slug"
                  />

                  <div className="divider"></div>

                  <FilterSection
                    title="Thể loại"
                    items={theLoai}
                    filterKey="genre"
                  />

                  <div className="divider"></div>

                  <FilterSection
                    title="Quốc gia"
                    items={quocGia}
                    filterKey="country"
                  />

                  <div className="divider"></div>

                  <FilterSection
                    title="Sắp xếp"
                    items={SortFieldList}
                    filterKey="sort"
                    idKey="slug"
                  />

                  <div className="divider"></div>

                  <FilterSection
                    title="Năm phát hành"
                    items={YearList}
                    filterKey="year"
                    idKey="slug"
                  />
                </div>

                {/* Footer */}
                {getActiveFilterCount() > 0 && (
                  <div className="card-actions justify-between items-center mt-8 pt-6 border-t border-base-300">
                    <div className="flex items-center gap-2">
                      <div className="badge badge-primary badge-lg">
                        {getActiveFilterCount()}
                      </div>
                      <span className="text-base-content/70">
                        bộ lọc đã chọn
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={clearAllFilters}
                        className="btn btn-outline btn-sm"
                      >
                        Xóa tất cả
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setShowFilter(false)}
                        className="btn btn-primary btn-sm"
                      >
                        Ẩn bộ lọc
                      </motion.button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default FilterButton;
