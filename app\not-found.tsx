// "use client";
// import React from "react";
// import Link from "next/link";
// import { motion } from "framer-motion";
// import {
//   Home,
//   Search,
//   Film,
//   AlertTriangle,
//   Compass,
//   Star,
//   Play,
// } from "lucide-react";

// const NotFound = () => {
//   const containerVariants = {
//     hidden: { opacity: 0 },
//     visible: {
//       opacity: 1,
//       transition: {
//         delayChildren: 0.3,
//         staggerChildren: 0.2,
//       },
//     },
//   };

//   const itemVariants = {
//     hidden: { y: 20, opacity: 0 },
//     visible: {
//       y: 0,
//       opacity: 1,
//       transition: {
//         type: "spring",
//         damping: 25,
//         stiffness: 300,
//       },
//     },
//   };

//   const floatingVariants = {
//     animate: {
//       y: [-10, 10, -10],
//       transition: {
//         duration: 3,
//         repeat: Infinity,
//         ease: "easeInOut",
//       },
//     },
//   };

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-base-200 via-base-100 to-base-300 flex items-center justify-center p-4">
//       <motion.div
//         variants={containerVariants}
//         initial="hidden"
//         animate="visible"
//         className="max-w-4xl mx-auto text-center"
//       >
//         {/* Floating 404 Number */}
//         <motion.div
//           variants={floatingVariants}
//           animate="animate"
//           className="mb-8"
//         >
//           <div className="text-9xl md:text-[12rem] font-black text-primary/20 select-none">
//             404
//           </div>
//         </motion.div>

//         {/* Main Content Card */}
//         <motion.div
//           variants={itemVariants}
//           className="card bg-base-100 shadow-2xl border border-base-300 backdrop-blur-sm"
//         >
//           <div className="card-body p-8 md:p-12">
//             {/* Icon */}
//             <motion.div
//               variants={itemVariants}
//               className="flex justify-center mb-6"
//             >
//               <div className="p-4 bg-error/10 rounded-full">
//                 <AlertTriangle className="w-16 h-16 text-error" />
//               </div>
//             </motion.div>

//             {/* Title */}
//             <motion.h1
//               variants={itemVariants}
//               className="text-4xl md:text-5xl font-bold text-base-content mb-4"
//             >
//               Oops! Trang không tồn tại
//             </motion.h1>

//             {/* Description */}
//             <motion.p
//               variants={itemVariants}
//               className="text-lg text-base-content/70 mb-8 max-w-2xl mx-auto"
//             >
//               Trang bạn đang tìm kiếm có thể đã bị xóa, đổi tên hoặc tạm thời
//               không khả dụng. Hãy thử tìm kiếm phim yêu thích của bạn hoặc quay
//               về trang chủ.
//             </motion.p>

//             {/* Action Buttons */}
//             <motion.div
//               variants={itemVariants}
//               className="flex flex-col sm:flex-row gap-4 justify-center items-center"
//             >
//               <Link
//                 href="/"
//                 className="btn btn-primary btn-lg gap-2 min-w-[200px]"
//               >
//                 <Home className="w-5 h-5" />
//                 Về trang chủ
//               </Link>

//               <Link
//                 href="/phim-moi"
//                 className="btn btn-outline btn-lg gap-2 min-w-[200px]"
//               >
//                 <Film className="w-5 h-5" />
//                 Phim mới
//               </Link>
//             </motion.div>

//             {/* Divider */}
//             <motion.div variants={itemVariants} className="divider my-8">
//               Hoặc
//             </motion.div>

//             {/* Quick Links */}
//             <motion.div
//               variants={itemVariants}
//               className="grid grid-cols-1 md:grid-cols-3 gap-4"
//             >
//               <Link href="/the-loai" className="btn btn-ghost btn-lg gap-2">
//                 <Compass className="w-5 h-5" />
//                 Thể loại
//               </Link>

//               <Link href="/quoc-gia" className="btn btn-ghost btn-lg gap-2">
//                 <Star className="w-5 h-5" />
//                 Quốc gia
//               </Link>

//               <Link href="/history" className="btn btn-ghost btn-lg gap-2">
//                 <Play className="w-5 h-5" />
//                 Lịch sử xem
//               </Link>
//             </motion.div>
//           </div>
//         </motion.div>

//         {/* Footer Message */}
//         <motion.div
//           variants={itemVariants}
//           className="mt-8 text-base-content/50"
//         >
//           <p className="text-sm">
//             Nếu bạn nghĩ đây là lỗi, vui lòng{" "}
//             <Link href="/" className="link link-primary">
//               liên hệ với chúng tôi
//             </Link>
//           </p>
//         </motion.div>

//         {/* Decorative Elements */}
//         <div className="absolute top-10 left-10 opacity-10">
//           <motion.div
//             animate={{ rotate: 360 }}
//             transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
//           >
//             <Film className="w-24 h-24 text-primary" />
//           </motion.div>
//         </div>

//         <div className="absolute bottom-10 right-10 opacity-10">
//           <motion.div
//             animate={{ rotate: -360 }}
//             transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
//           >
//             <Search className="w-20 h-20 text-secondary" />
//           </motion.div>
//         </div>
//       </motion.div>
//     </div>
//   );
// };
// export default NotFound;
