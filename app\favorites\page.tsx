"use client";
import { usePlayerStore } from "@/store";
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import MovieCardEnhanced from "@/components/MovieCard/MovieCardEnhanced";
import {
  Heart,
  Grid3X3,
  List,
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Calendar,
  Star,
  Trash2,
} from "lucide-react";

type ViewMode = "grid" | "list";
type SortBy = "name" | "year" | "rating" | "dateAdded";
type SortOrder = "asc" | "desc";

const FavoritesPage = () => {
  const favorites = usePlayerStore((state) => state.listFavorite);
  const removeFavorite = usePlayerStore((state) => state.removeFavorite);

  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<SortBy>("dateAdded");
  const [sortOrder, setSortOrder] = useState<SortOrder>("desc");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedGenres, setSelectedGenres] = useState<string[]>([]);

  // Filter and sort favorites
  const filteredAndSortedFavorites = React.useMemo(() => {
    let filtered = favorites.filter(
      (movie) =>
        movie.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        movie.origin_name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // Filter by genres
    if (selectedGenres.length > 0) {
      filtered = filtered.filter((movie) =>
        movie.category?.some((cat) => selectedGenres.includes(cat.slug))
      );
    }

    // Sort
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "year":
          comparison = (a.year || 0) - (b.year || 0);
          break;
        case "rating":
          comparison =
            (a.tmdb?.vote_average || 0) - (b.tmdb?.vote_average || 0);
          break;
        case "dateAdded":
          // Assuming newer items are at the end of the array
          comparison = favorites.indexOf(a) - favorites.indexOf(b);
          break;
      }

      return sortOrder === "asc" ? comparison : -comparison;
    });

    return filtered;
  }, [favorites, searchQuery, selectedGenres, sortBy, sortOrder]);

  // Get all unique genres from favorites
  const availableGenres = React.useMemo(() => {
    const genres = new Set<string>();
    favorites.forEach((movie) => {
      movie.category?.forEach((cat) => {
        genres.add(cat.slug);
      });
    });
    return Array.from(genres);
  }, [favorites]);

  const handleRemoveFromFavorites = (movieId: string) => {
    removeFavorite(movieId);
  };

  const handleClearAll = () => {
    if (confirm("Bạn có chắc chắn muốn xóa tất cả phim yêu thích?")) {
      // Remove all favorites one by one since there's no clearFavorites method
      favorites.forEach((movie) => {
        removeFavorite(movie._id);
      });
    }
  };

  const toggleGenreFilter = (genre: string) => {
    setSelectedGenres((prev) =>
      prev.includes(genre) ? prev.filter((g) => g !== genre) : [...prev, genre]
    );
  };

  if (favorites.length === 0) {
    return (
      <div className="min-h-screen pt-20 bg-base-100">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center max-w-md mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <div className="w-24 h-24 mx-auto mb-6 bg-base-200 rounded-full flex items-center justify-center">
                <Heart className="w-12 h-12 text-base-content/30" />
              </div>
              <h1 className="text-3xl font-bold text-base-content mb-4">
                Chưa có phim yêu thích
              </h1>
              <p className="text-base-content/70 mb-8">
                Hãy thêm những bộ phim bạn yêu thích để xem lại sau nhé!
              </p>
              <a href="/" className="btn btn-primary gap-2">
                <Search className="w-4 h-4" />
                Khám phá phim
              </a>
            </motion.div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 bg-base-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-primary/10 rounded-xl">
                <Heart className="w-8 h-8 text-primary" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-base-content">
                  Phim yêu thích
                </h1>
                <p className="text-base-content/70">
                  {favorites.length} bộ phim •{" "}
                  {filteredAndSortedFavorites.length} hiển thị
                </p>
              </div>
            </div>

            {favorites.length > 0 && (
              <div className="flex items-center gap-2">
                <button
                  onClick={handleClearAll}
                  className="btn btn-outline btn-error btn-sm gap-2"
                >
                  <Trash2 className="w-4 h-4" />
                  Xóa tất cả
                </button>
              </div>
            )}
          </div>

          {/* Search and Controls */}
          <div className="card bg-base-200 shadow-sm">
            <div className="card-body p-4">
              <div className="flex flex-col lg:flex-row gap-4">
                {/* Search */}
                <div className="flex-1">
                  <div className="form-control">
                    <div className="input-group">
                      <span className="bg-base-300">
                        <Search className="w-4 h-4" />
                      </span>
                      <input
                        type="text"
                        placeholder="Tìm kiếm phim..."
                        className="input input-bordered flex-1"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* View Mode */}
                <div className="flex items-center gap-2">
                  <div className="btn-group">
                    <button
                      onClick={() => setViewMode("grid")}
                      className={`btn btn-sm ${
                        viewMode === "grid" ? "btn-primary" : "btn-outline"
                      }`}
                    >
                      <Grid3X3 className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setViewMode("list")}
                      className={`btn btn-sm ${
                        viewMode === "list" ? "btn-primary" : "btn-outline"
                      }`}
                    >
                      <List className="w-4 h-4" />
                    </button>
                  </div>

                  {/* Sort */}
                  <div className="dropdown dropdown-end">
                    <div
                      tabIndex={0}
                      role="button"
                      className="btn btn-outline btn-sm gap-2"
                    >
                      {sortOrder === "asc" ? (
                        <SortAsc className="w-4 h-4" />
                      ) : (
                        <SortDesc className="w-4 h-4" />
                      )}
                      Sắp xếp
                    </div>
                    <ul
                      tabIndex={0}
                      className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52"
                    >
                      <li>
                        <button onClick={() => setSortBy("dateAdded")}>
                          <Calendar className="w-4 h-4" />
                          Ngày thêm
                        </button>
                      </li>
                      <li>
                        <button onClick={() => setSortBy("name")}>
                          <span className="w-4 h-4 flex items-center justify-center text-xs font-bold">
                            A
                          </span>
                          Tên phim
                        </button>
                      </li>
                      <li>
                        <button onClick={() => setSortBy("year")}>
                          <Calendar className="w-4 h-4" />
                          Năm phát hành
                        </button>
                      </li>
                      <li>
                        <button onClick={() => setSortBy("rating")}>
                          <Star className="w-4 h-4" />
                          Đánh giá
                        </button>
                      </li>
                      <div className="divider my-1"></div>
                      <li>
                        <button
                          onClick={() =>
                            setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                          }
                        >
                          {sortOrder === "asc" ? (
                            <SortDesc className="w-4 h-4" />
                          ) : (
                            <SortAsc className="w-4 h-4" />
                          )}
                          {sortOrder === "asc" ? "Giảm dần" : "Tăng dần"}
                        </button>
                      </li>
                    </ul>
                  </div>

                  {/* Filters */}
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className={`btn btn-sm gap-2 ${
                      showFilters ? "btn-primary" : "btn-outline"
                    }`}
                  >
                    <Filter className="w-4 h-4" />
                    Lọc
                  </button>
                </div>
              </div>

              {/* Genre Filters */}
              <AnimatePresence>
                {showFilters && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="mt-4 pt-4 border-t border-base-300"
                  >
                    <div className="flex flex-wrap gap-2">
                      <span className="text-sm font-medium text-base-content/70 mr-2">
                        Thể loại:
                      </span>
                      {availableGenres.map((genre) => (
                        <button
                          key={genre}
                          onClick={() => toggleGenreFilter(genre)}
                          className={`btn btn-xs ${
                            selectedGenres.includes(genre)
                              ? "btn-primary"
                              : "btn-outline"
                          }`}
                        >
                          {genre}
                        </button>
                      ))}
                      {selectedGenres.length > 0 && (
                        <button
                          onClick={() => setSelectedGenres([])}
                          className="btn btn-xs btn-ghost text-error"
                        >
                          Xóa bộ lọc
                        </button>
                      )}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </motion.div>

        {/* Movies Grid/List */}
        <AnimatePresence mode="wait">
          {filteredAndSortedFavorites.length === 0 ? (
            <motion.div
              key="no-results"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center py-12"
            >
              <div className="w-16 h-16 mx-auto mb-4 bg-base-200 rounded-full flex items-center justify-center">
                <Search className="w-8 h-8 text-base-content/30" />
              </div>
              <h3 className="text-xl font-semibold text-base-content mb-2">
                Không tìm thấy phim nào
              </h3>
              <p className="text-base-content/70">
                Thử thay đổi từ khóa tìm kiếm hoặc bộ lọc
              </p>
            </motion.div>
          ) : (
            <motion.div
              key={`${viewMode}-${filteredAndSortedFavorites.length}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className={
                viewMode === "grid"
                  ? "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4"
                  : "space-y-4"
              }
            >
              {filteredAndSortedFavorites.map((movie, index) => (
                <MovieCardEnhanced
                  key={movie._id}
                  m={movie}
                  index={index}
                  variant={viewMode === "grid" ? "grid" : "detailed"}
                  showActions={true}
                  showRating={true}
                  showInfo={true}
                  onAddToFavorites={() => handleRemoveFromFavorites(movie._id)}
                />
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default FavoritesPage;
