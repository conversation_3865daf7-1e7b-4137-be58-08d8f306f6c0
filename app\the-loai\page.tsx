import React from "react";
import Link from "next/link";
import theLoai from "@/constants/theloai.json";
import { Film, Star, ArrowRight, Grid3X3 } from "lucide-react";

const TheLoaiPage = () => {
  return (
    <div className="min-h-screen pt-20 bg-base-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-3 mb-8">
          <div className="p-3 bg-primary/10 rounded-xl">
            <Grid3X3 className="w-8 h-8 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-base-content">
              Thể loại phim
            </h1>
            <p className="text-base-content/70">
              Khám phá phim theo thể loại yêu thích của bạn
            </p>
          </div>
        </div>

        {/* Stats */}
        <div className="stats shadow mb-8">
          <div className="stat">
            <div className="stat-figure text-primary">
              <Film className="w-8 h-8" />
            </div>
            <div className="stat-title">Tổng số thể loại</div>
            <div className="stat-value text-primary">{theLoai.length}</div>
            <div className="stat-desc">Đa dạng và phong phú</div>
          </div>
        </div>

        {/* Genre Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
          {theLoai.map((genre) => (
            <Link
              key={genre._id}
              href={`/the-loai/${genre.slug}`}
              className="group"
            >
              <div className="card bg-base-100 shadow-md border border-base-300 hover:shadow-xl hover:border-primary/50 transition-all duration-300 group-hover:scale-105">
                <div className="card-body p-4">
                  {/* Genre Icon */}
                  <div className="flex items-center justify-center mb-3">
                    <div className="p-3 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full group-hover:from-primary/30 group-hover:to-secondary/30 transition-colors">
                      <Star className="w-6 h-6 text-primary" />
                    </div>
                  </div>

                  {/* Genre Name */}
                  <h3 className="text-center font-semibold text-base-content group-hover:text-primary transition-colors">
                    {genre.name}
                  </h3>

                  {/* Arrow Icon */}
                  <div className="flex justify-center mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <ArrowRight className="w-4 h-4 text-primary" />
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TheLoaiPage;
