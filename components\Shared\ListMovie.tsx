import MovieCardEnhanced from "@/components/MovieCard/MovieCardEnhanced";
import { Item } from "@/type/ListMovieRespone";
import React from "react";

interface IProps {
  promise: Promise<{
    data: {
      items: Item[];
    };
  }>;
}

const ListMovie = async ({ promise }: IProps) => {
  const data = await promise;
  return (
    <div className="w-full flex justify-center">
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
        {data?.data?.items.map((item) => {
          return (
            <MovieCardEnhanced variant="compact" key={item._id} m={item} />
          );
        })}
      </div>
    </div>
  );
};

export default ListMovie;
